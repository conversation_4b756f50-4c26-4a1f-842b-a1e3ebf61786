import * as React from "react";

import { cn } from "@/lib/utils";

function Input({ className, type, ...props }: React.ComponentProps<"input">) {
	return (
		<input
			type={type}
			data-slot="input"
			className={cn(
				"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground bg-input border-border flex h-9 w-full min-w-0 rounded-none border-2 px-3 py-1 text-base shadow-sm transition-[color,box-shadow,border-color] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm font-pixelify",
				"focus-visible:border-primary focus-visible:ring-primary/30 focus-visible:ring-2",
				"aria-invalid:ring-destructive/30 aria-invalid:border-destructive",
				"hover:border-ring transition-colors duration-200",
				className,
			)}
			{...props}
		/>
	);
}

export { Input };
