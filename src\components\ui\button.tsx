import { cn } from "@/lib/utils";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

const buttonVariants = cva(
	"rounded-none active:translate-y-1 m-1.5 transition-transform relative inline-flex items-center justify-center gap-1.5 select-none cursor-pointer transition-all duration-200 disabled:cursor-not-allowed disabled:opacity-50 font-pixelify",
	{
		variants: {
			variant: {
				default: "bg-primary text-primary-foreground hover:bg-primary/90 shadow-lg",
				destructive: "bg-destructive text-primary-foreground hover:bg-destructive/90 shadow-lg",
				outline: "border-2 border-primary text-primary hover:bg-primary hover:text-primary-foreground",
				secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-md",
				ghost: "hover:text-primary hover:bg-accent",
				link: "text-primary underline-offset-4 hover:underline",
			},
			size: {
				default: "h-10 px-4 py-2 has-[>svg]:px-3 text-lg font-semibold",
				sm: "h-8 px-3 has-[>svg]:px-2.5 text-sm font-medium",
				lg: "h-12 px-8 has-[>svg]:px-4 text-2xl font-bold",
				icon: "size-10",
			},
		},
		defaultVariants: {
			variant: "default",
			size: "default",
		},
	},
);

export type ButtonProps = React.ButtonHTMLAttributes<HTMLButtonElement> &
	VariantProps<typeof buttonVariants> & {
		asChild?: boolean;
	};

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(({ className, variant, size, asChild = false, ...props }, ref) => {
	const Comp = asChild ? Slot : "button";
	const fullVariant = variant as NonNullable<VariantProps<typeof buttonVariants>["variant"]>;

	const renderPixelBorder = () => {
		if (variant === "ghost" || variant === "link") return null;

		const borderColor = variant === "outline" ? "border-primary" : "border-foreground dark:border-ring";
		const shadowColor =
			variant === "destructive"
				? "bg-destructive/30"
				: variant === "secondary"
					? "bg-secondary-foreground/20"
					: "bg-foreground/20 dark:bg-ring/20";

		return (
			<>
				{/* Top and bottom borders */}
				<div
					className={`absolute -top-1.5 w-1/2 left-1.5 h-1.5 ${borderColor === "border-primary" ? "bg-primary" : "bg-foreground dark:bg-ring"}`}
				/>
				<div
					className={`absolute -top-1.5 w-1/2 right-1.5 h-1.5 ${borderColor === "border-primary" ? "bg-primary" : "bg-foreground dark:bg-ring"}`}
				/>
				<div
					className={`absolute -bottom-1.5 w-1/2 left-1.5 h-1.5 ${borderColor === "border-primary" ? "bg-primary" : "bg-foreground dark:bg-ring"}`}
				/>
				<div
					className={`absolute -bottom-1.5 w-1/2 right-1.5 h-1.5 ${borderColor === "border-primary" ? "bg-primary" : "bg-foreground dark:bg-ring"}`}
				/>

				{/* Corner pixels */}
				<div
					className={`absolute top-0 left-0 size-1.5 ${borderColor === "border-primary" ? "bg-primary" : "bg-foreground dark:bg-ring"}`}
				/>
				<div
					className={`absolute top-0 right-0 size-1.5 ${borderColor === "border-primary" ? "bg-primary" : "bg-foreground dark:bg-ring"}`}
				/>
				<div
					className={`absolute bottom-0 left-0 size-1.5 ${borderColor === "border-primary" ? "bg-primary" : "bg-foreground dark:bg-ring"}`}
				/>
				<div
					className={`absolute bottom-0 right-0 size-1.5 ${borderColor === "border-primary" ? "bg-primary" : "bg-foreground dark:bg-ring"}`}
				/>

				{/* Side borders */}
				<div
					className={`absolute top-1.5 -left-1.5 h-2/3 w-1.5 ${borderColor === "border-primary" ? "bg-primary" : "bg-foreground dark:bg-ring"}`}
				/>
				<div
					className={`absolute top-1.5 -right-1.5 h-2/3 w-1.5 ${borderColor === "border-primary" ? "bg-primary" : "bg-foreground dark:bg-ring"}`}
				/>

				{/* 3D shadow effect for filled buttons */}
				{fullVariant !== "outline" && fullVariant !== "ghost" && fullVariant !== "link" && (
					<>
						<div className={`absolute top-0 left-0 w-full h-1.5 ${shadowColor}`} />
						<div className={`absolute top-1.5 left-0 w-3 h-1.5 ${shadowColor}`} />
						<div className={`absolute bottom-0 left-0 w-full h-1.5 ${shadowColor}`} />
						<div className={`absolute bottom-1.5 right-0 w-3 h-1.5 ${shadowColor}`} />
					</>
				)}
			</>
		);
	};

	const renderIconBorder = () => {
		const borderColor = variant === "outline" ? "bg-primary" : "bg-foreground dark:bg-ring";

		return (
			<>
				<div className={`absolute top-0 left-0 w-full h-1.5 ${borderColor}`} />
				<div className={`absolute bottom-0 w-full h-1.5 ${borderColor}`} />
				<div className={`absolute top-1 -left-1.5 w-1.5 h-1/2 ${borderColor}`} />
				<div className={`absolute bottom-1 -left-1.5 w-1.5 h-1/2 ${borderColor}`} />
				<div className={`absolute top-1 -right-1.5 w-1.5 h-1/2 ${borderColor}`} />
				<div className={`absolute bottom-1 -right-1.5 w-1.5 h-1/2 ${borderColor}`} />
			</>
		);
	};

	return (
		<Comp className={cn(buttonVariants({ variant, size, className }), "group")} ref={ref} {...props}>
			{asChild ? (
				<span className="relative inline-flex items-center justify-center gap-1.5">
					{props.children}
					{size === "icon" ? renderIconBorder() : renderPixelBorder()}
				</span>
			) : (
				<>
					{props.children}
					{size === "icon" ? renderIconBorder() : renderPixelBorder()}
				</>
			)}
		</Comp>
	);
});
