"use client";

import * as DropdownMenuPrimitive from "@radix-ui/react-dropdown-menu";
import { cva, type VariantProps } from "class-variance-authority";
import { CheckIcon, ChevronRightIcon, CircleIcon } from "lucide-react";
import * as React from "react";

import { cn } from "@/lib/utils";

export const dropDownVariants = cva("", {
	variants: {
		font: {
			normal: "",
			retro: "font-pixelify",
		},
	},
	defaultVariants: {
		font: "retro",
	},
});

export type DropdownMenuContentProps = React.ComponentProps<typeof DropdownMenuPrimitive.Content> & VariantProps<typeof dropDownVariants>;

export const DropdownMenu = DropdownMenuPrimitive.Root;
export const DropdownMenuPortal = DropdownMenuPrimitive.Portal;
export const DropdownMenuTrigger = DropdownMenuPrimitive.Trigger;
export const DropdownMenuGroup = DropdownMenuPrimitive.Group;
export const DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup;
export const DropdownMenuSub = DropdownMenuPrimitive.Sub;

export const DropdownMenuContent = ({ children, className, font, ...props }: DropdownMenuContentProps) => (
	<DropdownMenuPrimitive.Portal>
		<DropdownMenuPrimitive.Content
			data-slot="dropdown-menu-content"
			sideOffset={4}
			className={cn(
				"z-50 min-w-[12rem] overflow-hidden rounded-none bg-popover p-2 text-popover-foreground",
				"border-4 border-foreground dark:border-ring",
				"shadow-[8px_8px_0px_0px_rgba(0,0,0,0.3)]",
				"data-[state=open]:animate-in data-[state=closed]:animate-out",
				"data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
				"data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
				"data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2",
				"data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
				dropDownVariants({ font }),
				className,
			)}
			{...props}>
			{children}
		</DropdownMenuPrimitive.Content>
	</DropdownMenuPrimitive.Portal>
);

export const DropdownMenuSubContent = ({ children, className, font, ...props }: DropdownMenuContentProps) => (
	<DropdownMenuPrimitive.SubContent
		data-slot="dropdown-menu-sub-content"
		className={cn(
			"z-50 min-w-[12rem] overflow-hidden rounded-none bg-popover p-2 text-popover-foreground",
			"border-4 border-foreground dark:border-ring",
			"shadow-[8px_8px_0px_0px_rgba(0,0,0,0.3)]",
			"data-[state=open]:animate-in data-[state=closed]:animate-out",
			"data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
			"data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
			"data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2",
			"data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
			dropDownVariants({ font }),
			className,
		)}
		{...props}>
		{children}
	</DropdownMenuPrimitive.SubContent>
);

export const DropdownMenuItem = ({ className, children, ...props }: React.ComponentProps<typeof DropdownMenuPrimitive.Item>) => (
	<DropdownMenuPrimitive.Item
		data-slot="dropdown-menu-item"
		className={cn(
			"relative flex cursor-default select-none items-center rounded-none px-3 py-2 text-sm outline-none",
			"transition-all duration-150 ease-in-out overflow-hidden",
			"hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
			"hover:translate-y-[-1px] active:translate-y-0",
			"data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
			"group",
			className,
		)}
		{...props}>
		{/* Pixel borders - hidden by default, shown on hover/focus */}
		<div className="absolute inset-0 opacity-0 group-hover:opacity-100 group-focus:opacity-100 transition-opacity duration-150">
			{/* Top and bottom borders */}
			<div className="absolute -top-1.5 w-1/2 left-1 h-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute -top-1.5 w-1/2 right-1 h-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute -bottom-1.5 w-1/2 left-1 h-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute -bottom-1.5 w-1/2 right-1 h-1.5 bg-foreground dark:bg-ring" />

			{/* Corner pixels */}
			<div className="absolute top-0 left-0 size-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute top-0 right-0 size-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute bottom-0 left-0 size-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute bottom-0 right-0 size-1.5 bg-foreground dark:bg-ring" />

			{/* Side borders */}
			<div className="absolute top-1.5 -left-1.5 h-2/3 w-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute top-1.5 -right-1.5 h-2/3 w-1.5 bg-foreground dark:bg-ring" />
		</div>

		{/* Active state shadow */}
		<div className="absolute inset-0 opacity-0 group-active:opacity-100 transition-opacity duration-75">
			<div className="absolute bottom-1 right-1 w-full h-1.5 bg-foreground/30 dark:bg-ring/30" />
			<div className="absolute bottom-1.5 right-1.5 w-2 h-1.5 bg-foreground/30 dark:bg-ring/30" />
		</div>

		{children}
	</DropdownMenuPrimitive.Item>
);

export const DropdownMenuSubTrigger = ({
	className,
	children,
	...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger>) => (
	<DropdownMenuPrimitive.SubTrigger
		data-slot="dropdown-menu-sub-trigger"
		className={cn(
			"relative flex cursor-default select-none items-center rounded-none px-3 py-2 text-sm outline-none",
			"transition-all duration-150 ease-in-out overflow-hidden",
			"hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
			"hover:translate-y-[-1px] active:translate-y-0",
			"data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
			"data-[state=open]:bg-accent data-[state=open]:text-accent-foreground",
			"data-[state=open]:translate-y-[-1px]",
			"group",
			className,
		)}
		{...props}>
		{/* Pixel borders - shown on hover/focus/open */}
		<div className="absolute inset-0 opacity-0 group-hover:opacity-100 group-focus:opacity-100 group-data-[state=open]:opacity-100 transition-opacity duration-150">
			{/* Top and bottom borders */}
			<div className="absolute -top-1.5 w-1/2 left-1 h-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute -top-1.5 w-1/2 right-1 h-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute -bottom-1.5 w-1/2 left-1 h-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute -bottom-1.5 w-1/2 right-1 h-1.5 bg-foreground dark:bg-ring" />

			{/* Corner pixels */}
			<div className="absolute top-0 left-0 size-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute top-0 right-0 size-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute bottom-0 left-0 size-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute bottom-0 right-0 size-1.5 bg-foreground dark:bg-ring" />

			{/* Side borders */}
			<div className="absolute top-1.5 -left-1.5 h-2/3 w-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute top-1.5 -right-1.5 h-2/3 w-1.5 bg-foreground dark:bg-ring" />
		</div>

		{/* Active state shadow */}
		<div className="absolute inset-0 opacity-0 group-active:opacity-100 transition-opacity duration-75">
			<div className="absolute bottom-1 right-1 w-full h-1.5 bg-foreground/30 dark:bg-ring/30" />
			<div className="absolute bottom-1.5 right-1.5 w-2 h-1.5 bg-foreground/30 dark:bg-ring/30" />
		</div>

		{children}
		<ChevronRightIcon className="ml-auto size-4" />
	</DropdownMenuPrimitive.SubTrigger>
);

export const DropdownMenuLabel = ({
	className,
	inset,
	...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & { inset?: boolean }) => (
	<DropdownMenuPrimitive.Label
		data-slot="dropdown-menu-label"
		data-inset={inset}
		className={cn("px-3 py-2 text-xs font-bold text-foreground uppercase tracking-wider", inset && "pl-8", className)}
		{...props}
	/>
);

export const DropdownMenuSeparator = ({ className, ...props }: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) => (
	<DropdownMenuPrimitive.Separator
		data-slot="dropdown-menu-separator"
		className={cn(
			"-mx-1 my-2 h-[2px] bg-gradient-to-r from-transparent via-muted to-transparent",
			"bg-[length:4px_2px] bg-repeat-x",
			className,
		)}
		{...props}
	/>
);

export const DropdownMenuShortcut = ({ className, ...props }: React.ComponentProps<"span">) => (
	<span
		data-slot="dropdown-menu-shortcut"
		className={cn(
			"ml-auto text-xs tracking-wider text-muted-foreground font-mono",
			"bg-muted/50 px-1.5 py-0.5 rounded-none border-2 border-muted",
			"shadow-[2px_2px_0px_0px_rgba(0,0,0,0.1)]",
			className,
		)}
		{...props}
	/>
);

export const DropdownMenuCheckboxItem = ({
	className,
	children,
	checked,
	...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) => (
	<DropdownMenuPrimitive.CheckboxItem
		data-slot="dropdown-menu-checkbox-item"
		className={cn(
			"relative flex cursor-default select-none items-center rounded-none py-2 pl-8 pr-3 text-sm outline-none",
			"transition-all duration-150 ease-in-out overflow-hidden",
			"hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
			"hover:translate-y-[-1px] active:translate-y-0",
			"data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
			"group",
			className,
		)}
		checked={checked}
		{...props}>
		{/* Pixel borders - shown on hover/focus */}
		<div className="absolute inset-0 opacity-0 group-hover:opacity-100 group-focus:opacity-100 transition-opacity duration-150">
			{/* Top and bottom borders */}
			<div className="absolute -top-1.5 w-1/2 left-1 h-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute -top-1.5 w-1/2 right-1 h-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute -bottom-1.5 w-1/2 left-1 h-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute -bottom-1.5 w-1/2 right-1 h-1.5 bg-foreground dark:bg-ring" />

			{/* Corner pixels */}
			<div className="absolute top-0 left-0 size-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute top-0 right-0 size-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute bottom-0 left-0 size-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute bottom-0 right-0 size-1.5 bg-foreground dark:bg-ring" />

			{/* Side borders */}
			<div className="absolute top-1.5 -left-1.5 h-2/3 w-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute top-1.5 -right-1.5 h-2/3 w-1.5 bg-foreground dark:bg-ring" />
		</div>

		{/* Active state shadow */}
		<div className="absolute inset-0 opacity-0 group-active:opacity-100 transition-opacity duration-75">
			<div className="absolute bottom-1 right-1 w-full h-1.5 bg-foreground/30 dark:bg-ring/30" />
			<div className="absolute bottom-1.5 right-1.5 w-2 h-1.5 bg-foreground/30 dark:bg-ring/30" />
		</div>

		<span className="absolute left-2 flex size-4 items-center justify-center border border-muted rounded-none bg-background">
			<DropdownMenuPrimitive.ItemIndicator>
				<CheckIcon className="size-3" />
			</DropdownMenuPrimitive.ItemIndicator>
		</span>
		{children}
	</DropdownMenuPrimitive.CheckboxItem>
);

export const DropdownMenuRadioItem = ({ className, children, ...props }: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) => (
	<DropdownMenuPrimitive.RadioItem
		data-slot="dropdown-menu-radio-item"
		className={cn(
			"relative flex cursor-default select-none items-center rounded-none py-2 pl-8 pr-3 text-sm outline-none",
			"transition-all duration-150 ease-in-out overflow-hidden",
			"hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
			"hover:translate-y-[-1px] active:translate-y-0",
			"data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
			"group",
			className,
		)}
		{...props}>
		{/* Pixel borders - shown on hover/focus */}
		<div className="absolute inset-0 opacity-0 group-hover:opacity-100 group-focus:opacity-100 transition-opacity duration-150">
			{/* Top and bottom borders */}
			<div className="absolute -top-1.5 w-1/2 left-1 h-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute -top-1.5 w-1/2 right-1 h-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute -bottom-1.5 w-1/2 left-1 h-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute -bottom-1.5 w-1/2 right-1 h-1.5 bg-foreground dark:bg-ring" />

			{/* Corner pixels */}
			<div className="absolute top-0 left-0 size-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute top-0 right-0 size-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute bottom-0 left-0 size-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute bottom-0 right-0 size-1.5 bg-foreground dark:bg-ring" />

			{/* Side borders */}
			<div className="absolute top-1.5 -left-1.5 h-2/3 w-1.5 bg-foreground dark:bg-ring" />
			<div className="absolute top-1.5 -right-1.5 h-2/3 w-1.5 bg-foreground dark:bg-ring" />
		</div>

		{/* Active state shadow */}
		<div className="absolute inset-0 opacity-0 group-active:opacity-100 transition-opacity duration-75">
			<div className="absolute bottom-1 right-1 w-full h-1.5 bg-foreground/30 dark:bg-ring/30" />
			<div className="absolute bottom-1.5 right-1.5 w-2 h-1.5 bg-foreground/30 dark:bg-ring/30" />
		</div>

		<span className="absolute left-2 flex size-4 items-center justify-center">
			<div className="size-3 rounded-none border border-muted bg-background flex items-center justify-center">
				<DropdownMenuPrimitive.ItemIndicator>
					<CircleIcon className="size-1.5 fill-current" />
				</DropdownMenuPrimitive.ItemIndicator>
			</div>
		</span>
		{children}
	</DropdownMenuPrimitive.RadioItem>
);
