/* City Theme - Inspired by cyberpunk and retro-futuristic aesthetics */

.appearance-city {
	/* Base colors - clean white with cool blue accents */
	--background: oklch(0.98 0.01 240);
	--foreground: oklch(0.2 0.05 240);
	--card: oklch(0.99 0.008 240);
	--card-foreground: oklch(0.2 0.05 240);
	--popover: oklch(0.99 0.008 240);
	--popover-foreground: oklch(0.2 0.05 240);

	/* Primary - electric blue */
	--primary: oklch(0.6 0.25 260);
	--primary-foreground: oklch(0.98 0.01 240);

	/* Secondary - cool gray */
	--secondary: oklch(0.88 0.02 240);
	--secondary-foreground: oklch(0.3 0.04 240);

	/* Muted - light blue-gray */
	--muted: oklch(0.92 0.015 240);
	--muted-foreground: oklch(0.5 0.03 240);

	/* Accent - neon cyan */
	--accent: oklch(0.75 0.2 200);
	--accent-foreground: oklch(0.2 0.05 240);

	/* Destructive - hot pink */
	--destructive: oklch(0.65 0.28 340);

	/* Borders and inputs */
	--border: oklch(0.85 0.02 240);
	--input: oklch(0.95 0.01 240);
	--ring: oklch(0.6 0.25 260);

	/* Charts - neon tech palette */
	--chart-1: oklch(0.6 0.25 260); /* Electric blue */
	--chart-2: oklch(0.75 0.2 200); /* Neon cyan */
	--chart-3: oklch(0.65 0.28 340); /* Hot pink */
	--chart-4: oklch(0.7 0.22 300); /* Purple */
	--chart-5: oklch(0.8 0.18 60); /* Electric yellow */

	/* Sidebar */
	--sidebar: oklch(0.96 0.012 240);
	--sidebar-foreground: oklch(0.2 0.05 240);
	--sidebar-primary: oklch(0.6 0.25 260);
	--sidebar-primary-foreground: oklch(0.98 0.01 240);
	--sidebar-accent: oklch(0.92 0.015 240);
	--sidebar-accent-foreground: oklch(0.3 0.04 240);
	--sidebar-border: oklch(0.85 0.02 240);
	--sidebar-ring: oklch(0.6 0.25 260);
}

.appearance-city.dark {
	/* Base colors - dark cyberpunk night with neon highlights */
	--background: oklch(0.08 0.02 240);
	--foreground: oklch(0.9 0.02 200);
	--card: oklch(0.12 0.03 240);
	--card-foreground: oklch(0.9 0.02 200);
	--popover: oklch(0.12 0.03 240);
	--popover-foreground: oklch(0.9 0.02 200);

	/* Primary - bright neon blue */
	--primary: oklch(0.75 0.3 260);
	--primary-foreground: oklch(0.08 0.02 240);

	/* Secondary - dark steel */
	--secondary: oklch(0.2 0.03 240);
	--secondary-foreground: oklch(0.85 0.02 200);

	/* Muted - dark blue-gray */
	--muted: oklch(0.15 0.02 240);
	--muted-foreground: oklch(0.65 0.03 200);

	/* Accent - electric cyan */
	--accent: oklch(0.8 0.25 200);
	--accent-foreground: oklch(0.08 0.02 240);

	/* Destructive - neon magenta */
	--destructive: oklch(0.7 0.35 340);

	/* Borders and inputs */
	--border: oklch(0.25 0.04 240);
	--input: oklch(0.1 0.02 240);
	--ring: oklch(0.75 0.3 260);

	/* Charts - cyberpunk neon palette */
	--chart-1: oklch(0.75 0.3 260); /* Neon blue */
	--chart-2: oklch(0.8 0.25 200); /* Electric cyan */
	--chart-3: oklch(0.7 0.35 340); /* Neon magenta */
	--chart-4: oklch(0.75 0.28 300); /* Electric purple */
	--chart-5: oklch(0.85 0.22 60); /* Neon yellow */

	/* Sidebar */
	--sidebar: oklch(0.06 0.02 240);
	--sidebar-foreground: oklch(0.9 0.02 200);
	--sidebar-primary: oklch(0.75 0.3 260);
	--sidebar-primary-foreground: oklch(0.08 0.02 240);
	--sidebar-accent: oklch(0.15 0.02 240);
	--sidebar-accent-foreground: oklch(0.85 0.02 200);
	--sidebar-border: oklch(0.25 0.04 240);
	--sidebar-ring: oklch(0.75 0.3 260);
}
