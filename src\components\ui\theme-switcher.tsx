"use client";

import { useAppearance } from "@/components/providers/appearance-provider";
import { Button } from "@/components/ui/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown";
import { Moon, Palette, Sun, Trees, Building, Castle } from "lucide-react";

export const ThemeSwitcher = () => {
	const { appearance, setAppearance, theme, setTheme, resolvedTheme } = useAppearance();

	const appearanceIcons = {
		forest: Trees,
		city: Building,
		dungeon: Castle,
	};

	const appearanceLabels = {
		forest: "Forest",
		city: "City",
		dungeon: "Dungeon",
	};

	const CurrentAppearanceIcon = appearanceIcons[appearance];

	return (
		<div className="flex items-center gap-2">
			{/* Appearance Switcher */}
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button variant="outline" size="icon" className="relative">
						<CurrentAppearanceIcon className="h-4 w-4" />
						<span className="sr-only">Switch appearance</span>
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="end" className="w-48">
					<DropdownMenuLabel>Appearance Theme</DropdownMenuLabel>
					<DropdownMenuSeparator />
					{Object.entries(appearanceIcons).map(([key, Icon]) => (
						<DropdownMenuItem
							key={key}
							onClick={() => setAppearance(key as any)}
							className={`flex items-center gap-2 ${appearance === key ? "bg-accent" : ""}`}
						>
							<Icon className="h-4 w-4" />
							{appearanceLabels[key as keyof typeof appearanceLabels]}
							{appearance === key && (
								<div className="ml-auto h-2 w-2 rounded-full bg-primary" />
							)}
						</DropdownMenuItem>
					))}
				</DropdownMenuContent>
			</DropdownMenu>

			{/* Light/Dark Mode Switcher */}
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button variant="outline" size="icon">
						{resolvedTheme === "dark" ? (
							<Moon className="h-4 w-4" />
						) : (
							<Sun className="h-4 w-4" />
						)}
						<span className="sr-only">Toggle theme</span>
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent align="end">
					<DropdownMenuLabel>Theme Mode</DropdownMenuLabel>
					<DropdownMenuSeparator />
					<DropdownMenuItem
						onClick={() => setTheme("light")}
						className={`flex items-center gap-2 ${theme === "light" ? "bg-accent" : ""}`}
					>
						<Sun className="h-4 w-4" />
						Light
						{theme === "light" && (
							<div className="ml-auto h-2 w-2 rounded-full bg-primary" />
						)}
					</DropdownMenuItem>
					<DropdownMenuItem
						onClick={() => setTheme("dark")}
						className={`flex items-center gap-2 ${theme === "dark" ? "bg-accent" : ""}`}
					>
						<Moon className="h-4 w-4" />
						Dark
						{theme === "dark" && (
							<div className="ml-auto h-2 w-2 rounded-full bg-primary" />
						)}
					</DropdownMenuItem>
					<DropdownMenuItem
						onClick={() => setTheme("system")}
						className={`flex items-center gap-2 ${theme === "system" ? "bg-accent" : ""}`}
					>
						<Palette className="h-4 w-4" />
						System
						{theme === "system" && (
							<div className="ml-auto h-2 w-2 rounded-full bg-primary" />
						)}
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>
		</div>
	);
};
